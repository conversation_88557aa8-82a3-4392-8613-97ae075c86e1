import { Command } from 'commander'
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

import packageJson from '../package.json' with { type: 'json' }

import * as cli from './cli.js'
import type { CommandOptions as CliCommandOptions } from './types.js'
import * as utilities from './utils.js' // Corrected alias back to utils
// Mock dependencies
const mockDiscoverApps = vi.fn()
const mockCheckHomebrewInstalled = vi.fn()
vi.mock('./app-scanner.js', () => ({
  checkHomebrewInstalled: mockCheckHomebrewInstalled,
  discoverApps: mockDiscoverApps,
}))
const mockPromptAppSelection = vi.fn()
const mockPromptSudoPassword = vi.fn()
const mockPromptConfirmation = vi.fn()
const mockDisplayWelcome = vi.fn()
const mockDisplayInstallationPlan = vi.fn()
const mockDisplayFinalSummary = vi.fn()
const mockDisplayTroubleshooting = vi.fn()
vi.mock('./prompts.js', () => ({
  displayFinalSummary: mockDisplayFinalSummary,
  displayInstallationPlan: mockDisplayInstallationPlan,
  displayTroubleshooting: mockDisplayTroubleshooting,
  displayWelcome: mockDisplayWelcome,
  promptAppSelection: mockPromptAppSelection,
  promptConfirmation: mockPromptConfirmation,
  promptSudoPassword: mockPromptSudoPassword,
}))
const mockInstallApps = vi.fn()
const mockValidateInstallationPrerequisites = vi.fn()
const mockGetInstallationSummary = vi.fn()
vi.mock('./installer.js', () => ({
  getInstallationSummary: mockGetInstallationSummary,
  installApps: mockInstallApps,
  validateInstallationPrerequisites: mockValidateInstallationPrerequisites,
}))

const mockHandleError = vi.fn((error: Error) => {
  if (error.message.includes('process.exit')) {
    throw error
  }

  console.error('Mocked handleError called with:', error.message)
  throw error
})
const mockInitializeErrorHandler = vi.fn(() => ({ handleError: mockHandleError }))
const mockGetErrorHandler = vi.fn(() => ({ handleError: mockHandleError }))
const mockSetupGlobalErrorHandlers = vi.fn()
vi.mock('./error-handler.js', () => ({
  ErrorHandler: vi.fn().mockImplementation(() => ({
    handleError: mockHandleError,
  })),
  getErrorHandler: mockGetErrorHandler,
  initializeErrorHandler: mockInitializeErrorHandler,
  setupGlobalErrorHandlers: mockSetupGlobalErrorHandlers,
}))

// Logger instance for tests to assert against
const testLogger = {
  debug: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  verbose: vi.fn(),
  warn: vi.fn(),
}

// Correctly spy on the createLogger in the already imported utils module
vi.spyOn(utilities, 'createLogger').mockReturnValue(testLogger)

// Spy on process.exit
const mockProcessExit = vi.spyOn(process, 'exit').mockImplementation((code?: null | number | string | undefined): never => {
  throw new Error(`process.exit: ${code ?? 'undefined'}`)
})
// Global console mocks for tests that use them directly
const mockConsoleLog = vi.fn()
const mockConsoleWarn = vi.fn()
describe('cLI', () => {
  let program: Command
  beforeEach(() => {
    vi.resetAllMocks()
    // Spy on console methods used by some CLI functions if not using the logger
    vi.spyOn(console, 'log').mockImplementation(mockConsoleLog)
    vi.spyOn(console, 'warn').mockImplementation(mockConsoleWarn)
    program = cli.createProgram()
    process.argv = ['node', 'cli.js'] // Reset argv
  })
  afterEach(() => {
    vi.restoreAllMocks()
  })
  describe('createProgram', () => {
    it('should configure program details correctly', () => {
      expect(program.name()).toBe('convert-apps-to-homebrew')
      expect(program.description()).toBe('Convert macOS applications to Homebrew installations with interactive selection')
      expect(program.version()).toBe(packageJson.version)
    })
    it('should define options correctly', () => {
      const options = program.options.map(opt => opt.flags)
      expect(options).toContain('-i, --ignore <apps...>')
      expect(options).toContain('-d, --dry-run')
      expect(options).toContain('--verbose')
      expect(options).toContain('--applications-dir <path>')
      expect(options).toContain('-v, --version')
      expect(options).toContain('-h, --help')
    })
  })
  describe('parseArguments', () => {
    it('should parse default options correctly', () => {
      const options = cli.parseArguments(['node', 'cli.js'])
      expect(options.dryRun).toBe(false)
      expect(options.verbose).toBe(false)
      expect(options.ignore).toEqual([])
      expect(options.applicationsDir).toBe('/Applications')
    })
    it('should parse --dry-run', () => {
      const options = cli.parseArguments(['node', 'cli.js', '--dry-run'])
      expect(options.dryRun).toBe(true)
    })
    it('should parse --verbose', () => {
      const options = cli.parseArguments(['node', 'cli.js', '--verbose'])
      expect(options.verbose).toBe(true)
    })
    it('should parse --ignore with multiple apps', () => {
      const options = cli.parseArguments(['node', 'cli.js', '--ignore', 'App1', 'App Two'])
      expect(options.ignore).toEqual(['App1', 'App Two'])
    })
    it('should parse --ignore with single app', () => {
      const options = cli.parseArguments(['node', 'cli.js', '--ignore', 'App1'])
      expect(options.ignore).toEqual(['App1'])
    })
    it('should parse --applications-dir', () => {
      const options = cli.parseArguments(['node', 'cli.js', '--applications-dir', '/MyApps'])
      expect(options.applicationsDir).toBe('/MyApps')
    })
    it('should exit if commander.helpDisplayed error occurs', () => {
      const programMockInstance: any = new Command()
      vi.spyOn(programMockInstance, 'parse').mockImplementation(() => {
        const error: any = new Error('Help displayed')
        error.code = 'commander.helpDisplayed'
        throw error
      })
      vi.spyOn(programMockInstance, 'opts').mockReturnValue({})
      vi.spyOn(cli, 'createProgram').mockReturnValueOnce(programMockInstance)
      expect(() => cli.parseArguments(['node', 'cli.js', '--help'])).toThrow('process.exit: 0')
      expect(mockProcessExit).toHaveBeenCalledWith(0)
    })
    it('should exit if commander.version error occurs', () => {
      const programMockInstance: any = new Command()
      vi.spyOn(programMockInstance, 'parse').mockImplementation(() => {
        const error: any = new Error('Version displayed')
        error.code = 'commander.version'
        throw error
      })
      vi.spyOn(programMockInstance, 'opts').mockReturnValue({})
      vi.spyOn(cli, 'createProgram').mockReturnValueOnce(programMockInstance)
      expect(() => cli.parseArguments(['node', 'cli.js', '--version'])).toThrow('process.exit: 0')
      expect(mockProcessExit).toHaveBeenCalledWith(0)
    })
    it('should handle invalid app name in ignore list', () => {
      const programMockInstance: any = new Command()
      vi.spyOn(programMockInstance, 'parse').mockImplementation(() => programMockInstance)
      vi.spyOn(programMockInstance, 'opts').mockReturnValue({ ignore: [' '] })
      vi.spyOn(cli, 'createProgram').mockReturnValueOnce(programMockInstance)
      expect(() => cli.parseArguments(['node', 'cli.js', '--ignore', ' '])).toThrow('process.exit: 1')
      expect(testLogger.error).toHaveBeenCalledWith(expect.stringContaining('Command line parsing error: Invalid app name in ignore list: " "'))
      expect(mockProcessExit).toHaveBeenCalledWith(1)
    })
  })
  describe('displayWelcome', () => {
    it('should display welcome message and options', () => {
      const options: CliCommandOptions = { applicationsDir: '/TestApps', dryRun: true, ignore: ['IgnoredApp'], verbose: true }
      cli.displayWelcome(options)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Convert Apps to Homebrew'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('DRY RUN MODE'))
      expect(testLogger.info).toHaveBeenCalledWith('Scanning directory: /TestApps')
      expect(testLogger.info).toHaveBeenCalledWith('Ignoring apps: IgnoredApp')
      expect(testLogger.verbose).toHaveBeenCalledWith('Verbose mode enabled')
    })
  })
  describe('displayTroubleshooting', () => {
    it('should display troubleshooting information', () => {
      cli.displayTroubleshooting()
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Troubleshooting'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Homebrew not installed:'))
    })
  })
  describe('setupSignalHandlers', () => {
    it('should set up SIGINT, SIGTERM, uncaughtException, and unhandledRejection listeners', () => {
      const onSpy = vi.spyOn(process, 'on')
      cli.setupSignalHandlers()
      expect(onSpy).toHaveBeenCalledWith('SIGINT', expect.any(Function))
      expect(onSpy).toHaveBeenCalledWith('SIGTERM', expect.any(Function))
      expect(onSpy).toHaveBeenCalledWith('uncaughtException', expect.any(Function))
      expect(onSpy).toHaveBeenCalledWith('unhandledRejection', expect.any(Function))
    })
  })
  describe('validateEnvironment', () => {
    const originalPlatform = process.platform
    const originalVersion = process.version
    afterEach(() => {
      Object.defineProperty(process, 'platform', { configurable: true, value: originalPlatform, writable: true })
      Object.defineProperty(process, 'version', { configurable: true, value: originalVersion, writable: true })
    })
    it('should not exit if Node version is >= 22 and platform is darwin', () => {
      Object.defineProperty(process, 'platform', { value: 'darwin', writable: true })
      Object.defineProperty(process, 'version', { value: 'v22.0.0', writable: true })
      expect(() => cli.validateEnvironment()).not.toThrow()
      expect(mockProcessExit).not.toHaveBeenCalled()
    })
    it('should exit if Node version is < 22', () => {
      Object.defineProperty(process, 'platform', { value: 'darwin', writable: true })
      Object.defineProperty(process, 'version', { value: 'v20.0.0', writable: true })
      expect(() => cli.validateEnvironment()).toThrow('process.exit: 1')
      expect(testLogger.error).toHaveBeenCalledWith(expect.stringContaining('Node.js version v20.0.0 is not supported.'))
      expect(mockProcessExit).toHaveBeenCalledWith(1)
    })
    it('should exit if platform is not darwin', () => {
      Object.defineProperty(process, 'platform', { value: 'linux', writable: true })
      Object.defineProperty(process, 'version', { value: 'v22.0.0', writable: true })
      expect(() => cli.validateEnvironment()).toThrow('process.exit: 1')
      expect(testLogger.error).toHaveBeenCalledWith('This tool is designed for macOS only.')
      expect(mockProcessExit).toHaveBeenCalledWith(1)
    })
  })
})
