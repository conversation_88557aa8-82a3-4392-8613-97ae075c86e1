import { promises as fsPromisesActual } from 'node:fs'

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

import { BREW_COMMANDS } from './constants.js'
import * as installer from './installer.js'
import type { AppInfo, InstallationResult, InstallerConfig, PackageInstallResult } from './types.js'
import { ConvertAppsError, ErrorType } from './types.js'
// Mock child_process.exec
const mockExecAsync = vi.fn()
vi.mock('node:util', async (importOriginal) => {
  const originalModule = await importOriginal<typeof import('node:util')>()

  return {
    ...originalModule,
    promisify: vi.fn((function_: (...arguments_: any[]) => any) => {
      if (typeof function_ === 'function' && function_.name === 'exec') {
        return mockExecAsync
      }

      return vi.fn()
    }),
  }
})
vi.mock('node:child_process', () => ({ exec: vi.fn() }))
// Mock fs promises
vi.mock('node:fs', async () => {
  const actualFs = await vi.importActual<typeof import('node:fs')>('node:fs')

  return {
    ...actualFs,
    promises: {
      ...actualFs.promises,
      access: vi.fn(),
      rm: vi.fn(),
    },
  }
})
const mockedFsAccess = vi.mocked(fsPromisesActual.access)
const mockSuccessfulCommand = (stdout = ''): { exitCode: number, stderr: string, stdout: string, success: boolean } => ({ exitCode: 0, stderr: '', stdout, success: true })
const baseAppCask: AppInfo = { alreadyInstalled: false, appPath: '/Applications/Test Cask.app', brewName: 'test-cask', brewType: 'cask', originalName: 'Test Cask', status: 'available' }
const baseAppFormula: AppInfo = { alreadyInstalled: false, appPath: '/Applications/Test Formula.app', brewName: 'test-formula', brewType: 'formula', originalName: 'Test Formula', status: 'available' }
const baseConfig: InstallerConfig = { dryRun: false, sudoPassword: 'testpassword', verbose: false }
const prereqCall = async () => installer.validateInstallationPrerequisites()
describe('installer', () => {
  beforeEach(() => {
    vi.resetAllMocks()
    mockedFsAccess.mockResolvedValue()
    mockExecAsync.mockResolvedValue({ stderr: '', stdout: '' })
  })
  describe('installApps', () => {
    it('should return empty result if no apps selected', async () => {
      const result = await installer.installApps([], baseConfig)
      expect(result.installed).toEqual([])
      expect(result.failed).toEqual([])
      expect(result.dryRun).toBe(false)
    })
    it('should install casks and formulas successfully', async () => {
      const apps = [baseAppCask, baseAppFormula]
      mockExecAsync.mockImplementation(async (command: string) => {
        if (command.includes(BREW_COMMANDS.INSTALL_CASK([baseAppCask.brewName]))) {
          return { stderr: '', stdout: 'Successfully installed 1 cask.' }
        }

        if (command.includes(BREW_COMMANDS.INSTALL_FORMULA([baseAppFormula.brewName]))) {
          return { stderr: '', stdout: 'Successfully installed 1 formula.' }
        }

        if (command.includes(`echo "${baseConfig.sudoPassword}" | sudo -S rm -rf`)) {
          return { stderr: '', stdout: '' }
        }

        return { stderr: '', stdout: 'unexpected command success' }
      })
      const result = await installer.installApps(apps, baseConfig)
      expect(result.installed.length).toBe(2)
      expect(result.failed.length).toBe(0)
      expect(result.installed.find(a => a.packageName === 'test-cask')).toBeDefined()
      expect(result.installed.find(a => a.packageName === 'test-formula')).toBeDefined()
      expect(mockExecAsync).toHaveBeenCalledWith(expect.stringContaining(`rm -rf "${baseAppCask.appPath}"`), expect.anything())
    })
    it('should handle failed cask installation', async () => {
      const apps = [baseAppCask]
      mockExecAsync.mockImplementation(async (command: string) => {
        if (command.includes(BREW_COMMANDS.INSTALL_CASK([baseAppCask.brewName]))) {
          const error = new Error('Cask installation failed') as Error & { code?: number, stderr?: string }
          error.stderr = 'Cask installation failed'
          error.code = 1
          throw error
        }
        throw new Error(`Unexpected command: ${command}`)
      })
      const result = await installer.installApps(apps, baseConfig)
      expect(result.installed.length).toBe(0)
      expect(result.failed.length).toBe(1)
      expect(result.failed.packageName).toBe('test-cask')
      expect(result.failed.error).toBe('Cask installation failed')
    })
    it('should handle failed formula installation', async () => {
      const apps = [baseAppFormula]
      mockExecAsync.mockImplementation(async (command: string) => {
        if (command.includes(BREW_COMMANDS.INSTALL_FORMULA([baseAppFormula.brewName]))) {
          const error = new Error('Formula installation failed') as Error & { code?: number, stderr?: string }
          error.stderr = 'Formula installation failed'
          error.code = 1
          throw error
        }
        throw new Error(`Unexpected command: ${command}`)
      })
      const result = await installer.installApps(apps, baseConfig)
      expect(result.installed.length).toBe(0)
      expect(result.failed.length).toBe(1)
      expect(result.failed.packageName).toBe('test-formula')
      expect(result.failed.error).toBe('Formula installation failed')
    })
    it('should run in dryRun mode', async () => {
      const apps = [baseAppCask]
      const dryRunConfig: InstallerConfig = { ...baseConfig, dryRun: true }
      mockExecAsync.mockResolvedValue({ stderr: '', stdout: '[DRY RUN] Would execute...' })
      const result = await installer.installApps(apps, dryRunConfig)
      expect(result.installed.length).toBe(1)

      if (result.installed.length > 0) {
        expect(result.installed.dryRun).toBe(true)
      }

      expect(result.failed.length).toBe(0)
      expect(mockExecAsync).toHaveBeenCalledWith(BREW_COMMANDS.INSTALL_CASK([baseAppCask.brewName]), expect.anything())
      expect(mockExecAsync).not.toHaveBeenCalledWith(expect.stringContaining(`echo "${baseConfig.sudoPassword}" | sudo -S rm -rf`), expect.anything())
    })
    it('should not delete original apps if sudo password is not provided', async () => {
      const apps = [baseAppCask]
      const noSudoConfig: InstallerConfig = { dryRun: false, sudoPassword: undefined, verbose: false }
      mockExecAsync.mockImplementation(async (command: string) => {
        if (command.includes(BREW_COMMANDS.INSTALL_CASK([baseAppCask.brewName]))) {
          return { stderr: '', stdout: 'Success' }
        }

        return { stderr: '', stdout: '' }
      })
      await installer.installApps(apps, noSudoConfig)
      expect(mockExecAsync).not.toHaveBeenCalledWith(expect.stringContaining('sudo -S rm -rf'), expect.anything())
    })
    it('should throw ConvertAppsError if main installation block fails unexpectedly', async () => {
      const apps = [baseAppCask]
      mockExecAsync.mockImplementation(async (command: string) => {
        if (command.includes(BREW_COMMANDS.INSTALL_CASK([baseAppCask.brewName]))) {
          throw new Error('Network issue during brew install')
        }

        return { stderr: '', stdout: 'unexpected success for other command' }
      })
      const installCall = async () => installer.installApps(apps, baseConfig)
      try {
        await installCall()
      }
      catch (error: any) {
        expect(error).toBeInstanceOf(ConvertAppsError)
        expect(error.type).toBe(ErrorType.COMMAND_FAILED)
        expect(error.message).toContain('Installation process failed: Network issue during brew install')
      }
    })
  })
  describe('validateInstallationPrerequisites', () => {
    it('should resolve if Homebrew is installed', async () => {
      mockExecAsync.mockResolvedValue(mockSuccessfulCommand('Homebrew 3.0.0'))
      await expect(prereqCall()).resolves.toBeUndefined()
    })
    it('should throw ConvertAppsError if Homebrew is not installed', async () => {
      const error = new Error('command not found') as Error & { code?: number, stderr?: string }
      error.stderr = 'command not found'
      error.code = 127
      mockExecAsync.mockRejectedValue(error)

      try {
        await prereqCall()
      }
      catch (error_: any) {
        expect(error_).toBeInstanceOf(ConvertAppsError)
        expect(error_.type).toBe(ErrorType.HOMEBREW_NOT_INSTALLED)
      }
    })
  })
  describe('getInstallationSummary', () => {
    it('should generate correct summary for mixed results', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: false,
        failed: [{ appName: 'App B', dryRun: false, error: 'network timeout', packageName: 'app-b', success: false }],
        ignored: [],
        installed: [{ appName: 'App A', dryRun: false, packageName: 'app-a', success: true }],
        unavailable: [],
      }
      const summary = installer.getInstallationSummary(result)
      expect(summary).toContain('INSTALLATION SUMMARY')
      expect(summary).toContain('Successfully installed: 1')
      expect(summary).toContain('â¢ App A (app-a)')
      expect(summary).toContain('Failed to install: 1')
      expect(summary).toContain('â¢ App B (app-b): network timeout')
    })
    it('should generate correct summary for dry run', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: true,
        failed: [],
        ignored: [],
        installed: [{ appName: 'App A', dryRun: true, packageName: 'app-a', success: true }],
        unavailable: [],
      }
      const summary = installer.getInstallationSummary(result)
      expect(summary).toContain('DRY RUN SUMMARY')
      expect(summary).toContain('Successfully installed: 1')
    })
    it('should state "No packages were processed" if empty results', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: false,
        failed: [],
        ignored: [],
        installed: [],
        unavailable: [],
      }
      const summary = installer.getInstallationSummary(result)
      expect(summary).toContain('No packages were processed')
    })
  })
})
