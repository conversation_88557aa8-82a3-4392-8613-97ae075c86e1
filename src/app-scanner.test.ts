import { promises as fsPromisesActual } from 'node:fs' // Renamed to avoid conflict
import path from 'node:path'

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

import * as appScanner from './app-scanner.js'
import { BREW_COMMANDS, DEFAULT_APPLICATIONS_DIR } from './constants.js'
import type { AppInfo, BrewCommandResult, ScannerConfig } from './types.js'
import { ConvertAppsError, ErrorType } from './types.js'
// Mock child_process.exec
const mockExecAsync = vi.fn()
vi.mock('node:util', async (importOriginal) => {
  const originalModule = await importOriginal<typeof import('node:util')>()

  return {
    ...originalModule,
    promisify: vi.fn((function_) => {
      if (typeof function_ === 'function' && function_.name === 'exec') {
        return mockExecAsync
      }

      return vi.fn() // Default mock for other promisified functions
    }),
  }
})
vi.mock('node:child_process', () => ({
  exec: vi.fn(),
}))
// Mock fs promises
vi.mock('node:fs', async () => {
  const actualFs = await vi.importActual<typeof import('node:fs')>('node:fs')

  return {
    ...actualFs,
    existsSync: vi.fn(),
    promises: {
      ...actualFs.promises,
      readdir: vi.fn(),
      realpath: vi.fn(),
      stat: vi.fn(),
    },
    realpathSync: vi.fn(),
  }
})
const mockedFsReaddir = vi.mocked(fsPromisesActual.readdir)

function mockExecuteCommandResult(stdout: string, stderr = '', exitCode = 0): BrewCommandResult {
  return {
    exitCode,
    stderr,
    stdout,
    success: exitCode === 0,
  }
}

function setupExecMock(commandPattern: RegExp | string, result: BrewCommandResult) {
  mockExecAsync.mockImplementation(async (cmdToExecute: string) => {
    let match = false
    if (typeof commandPattern === 'string') {
      // Simple check if the command starts with the base brew command (e.g., "brew info")
      // This might need to be more sophisticated for commands with dynamic parts not at the end.
      if (cmdToExecute.startsWith(commandPattern.slice(0, Math.max(0, commandPattern.indexOf(' ') > 0 ? commandPattern.indexOf(' ') : commandPattern.length)))) {
        // If commandPattern has quotes, check if the quoted part matches
        const quoteIndex = commandPattern.indexOf('"')

        if (quoteIndex === -1) {
          // No quotes, simple startsWith was likely enough or it's a command like "brew --version"
          match = cmdToExecute.startsWith(commandPattern)
        }
        else {
          const dynamicPartPattern = commandPattern.slice(Math.max(0, quoteIndex))

          if (cmdToExecute.includes(dynamicPartPattern)) {
            match = true
          }
        }
      }
    }
    else { // RegExp
      match = commandPattern.test(cmdToExecute)
    }

    if (match) {
      if (result.exitCode === 0) {
        return { stderr: result.stderr, stdout: result.stdout }
      }
      else {
        const error: Error & { code?: number, stderr?: string, stdout?: string } = new Error(result.stderr || `Command failed: ${cmdToExecute}`)
        error.stdout = result.stdout
        error.stderr = result.stderr
        error.code = result.exitCode

        throw error
      }
    }

    const unmockedError: Error & { code?: number } = new Error(`Unexpected command in mock: ${cmdToExecute}`)
    unmockedError.code = 1

    throw unmockedError
  })
}

describe('app Scanner', () => {
  const baseConfig: ScannerConfig = {
    applicationsDir: DEFAULT_APPLICATIONS_DIR,
    ignoredApps: [],
    verbose: false,
  }

  beforeEach(() => {
    vi.resetAllMocks()
    setupExecMock(BREW_COMMANDS.VERSION, mockExecuteCommandResult('Homebrew 3.6.0'))
    setupExecMock(BREW_COMMANDS.LIST_CASKS, mockExecuteCommandResult(''))
    setupExecMock(BREW_COMMANDS.LIST_FORMULAS, mockExecuteCommandResult(''))
    setupExecMock(/^brew info --cask ""[^"]+""/, mockExecuteCommandResult('', 'Error: No Cask with this name exists.', 1))
    setupExecMock(/^brew info ""[^"]+""$/, mockExecuteCommandResult('', 'Error: No available formula with this name.', 1))
    mockedFsReaddir.mockResolvedValue([])
  })
  describe('checkHomebrewInstalled', () => {
    it('should return true if brew --version succeeds', async () => {
      setupExecMock(BREW_COMMANDS.VERSION, mockExecuteCommandResult('Homebrew 3.6.0'))
      await expect(appScanner.checkHomebrewInstalled()).resolves.toBe(true)
    })
    it('should return false if brew --version fails', async () => {
      setupExecMock(BREW_COMMANDS.VERSION, mockExecuteCommandResult('', 'Error', 1))
      await expect(appScanner.checkHomebrewInstalled()).resolves.toBe(false)
    })
  })
  describe('scanApplicationsDirectory', () => {
    it('should return app paths for .app directories', async () => {
      mockedFsReaddir.mockResolvedValue([
        { isDirectory: () => true, name: 'App1.app' },
        { isDirectory: () => true, name: 'App2.App' },
        { isDirectory: () => false, name: 'NotAnApp.txt' },
        { isDirectory: () => true, name: 'StillNotAnApp' },
      ] as any)
      const appPaths = await appScanner.scanApplicationsDirectory('/TestApps')
      expect(appPaths).toEqual(['/TestApps/App1.app', '/TestApps/App2.App'])
      expect(fsPromisesActual.readdir).toHaveBeenCalledWith('/TestApps', { withFileTypes: true })
    })
    it('should throw ConvertAppsError if readdir fails with ENOENT', async () => {
      const enoentError = new Error('ENOENT error') as NodeJS.ErrnoException
      enoentError.code = 'ENOENT'
      mockedFsReaddir.mockRejectedValue(enoentError)
      await expect(appScanner.scanApplicationsDirectory('/NonExistent')).rejects.toThrow(ConvertAppsError)
      await expect(appScanner.scanApplicationsDirectory('/NonExistent')).rejects.toMatchObject({
        type: ErrorType.FILE_NOT_FOUND,
      })
    })
    it('should throw ConvertAppsError if readdir fails with EACCES', async () => {
      const eaccesError = new Error('EACCES error') as NodeJS.ErrnoException
      eaccesError.code = 'EACCES'
      mockedFsReaddir.mockRejectedValue(eaccesError)
      await expect(appScanner.scanApplicationsDirectory('/ProtectedDir')).rejects.toThrow(ConvertAppsError)
      await expect(appScanner.scanApplicationsDirectory('/ProtectedDir')).rejects.toMatchObject({
        type: ErrorType.PERMISSION_DENIED,
      })
    })
  })
  describe('determinePackageInfo', () => {
    it('should return cask if cask is available', async () => {
      setupExecMock(BREW_COMMANDS.INFO_CASK('test-app'), mockExecuteCommandResult('test-app: 1.0'))
      const info = await appScanner.determinePackageInfo('Test App', 'test-app')
      expect(info).toEqual({ alreadyInstalled: false, brewType: 'cask' })
    })
    it('should return formula if formula is available and cask is not', async () => {
      setupExecMock(BREW_COMMANDS.INFO_CASK('test-app'), mockExecuteCommandResult('', 'Error: No Cask with this name exists.', 1))
      setupExecMock(BREW_COMMANDS.INFO_FORMULA('test-app'), mockExecuteCommandResult('test-app: 1.0'))
      const info = await appScanner.determinePackageInfo('Test App', 'test-app')
      expect(info).toEqual({ alreadyInstalled: false, brewType: 'formula' })
    })
    it('should return unavailable if neither cask nor formula is available', async () => {
      setupExecMock(BREW_COMMANDS.INFO_CASK('test-app'), mockExecuteCommandResult('', 'Error: No Cask with this name exists.', 1))
      setupExecMock(BREW_COMMANDS.INFO_FORMULA('test-app'), mockExecuteCommandResult('', 'Error: No available formula with this name.', 1))
      const info = await appScanner.determinePackageInfo('Test App', 'test-app')
      expect(info).toEqual({ alreadyInstalled: false, brewType: 'unavailable' })
    })
  })
  describe('checkAlreadyInstalled', () => {
    const apps: AppInfo[] = [
      { alreadyInstalled: false, appPath: '', brewName: 'app1-cask', brewType: 'cask', originalName: 'App1', status: 'available' },
      { alreadyInstalled: false, appPath: '', brewName: 'app2-formula', brewType: 'formula', originalName: 'App2', status: 'available' },
      { alreadyInstalled: false, appPath: '', brewName: 'app3-cask', brewType: 'cask', originalName: 'App3', status: 'available' },
    ]

    it('should mark apps as alreadyInstalled and update status', async () => {
      setupExecMock(BREW_COMMANDS.LIST_CASKS, mockExecuteCommandResult('app1-cask\
other-cask'))
      setupExecMock(BREW_COMMANDS.LIST_FORMULAS, mockExecuteCommandResult('app2-formula\
other-formula'))
      const result = await appScanner.checkAlreadyInstalled([...apps])
      expect(result.find(a => a.brewName === 'app1-cask')?.alreadyInstalled).toBe(true)
      expect(result.find(a => a.brewName === 'app1-cask')?.status).toBe('already-installed')
      expect(result.find(a => a.brewName === 'app2-formula')?.alreadyInstalled).toBe(true)
      expect(result.find(a => a.brewName === 'app2-formula')?.status).toBe('already-installed')
      expect(result.find(a => a.brewName === 'app3-cask')?.alreadyInstalled).toBe(false)
      expect(result.find(a => a.brewName === 'app3-cask')?.status).toBe('available')
    })
  })
  describe('discoverApps', () => {
    it('should throw HOMEBREW_NOT_INSTALLED if brew is not found', async () => {
      setupExecMock(BREW_COMMANDS.VERSION, mockExecuteCommandResult('', 'Error', 127))
      await expect(appScanner.discoverApps(baseConfig)).rejects.toThrow(ConvertAppsError)
      await expect(appScanner.discoverApps(baseConfig)).rejects.toMatchObject({
        type: ErrorType.HOMEBREW_NOT_INSTALLED,
      })
    })
    it('should return empty array if no apps found in directory', async () => {
      mockedFsReaddir.mockResolvedValue([])
      const result = await appScanner.discoverApps(baseConfig)
      expect(result).toEqual([])
    })
    it('should process found apps, check availability and installed status', async () => {
      mockedFsReaddir.mockResolvedValue([
        { isDirectory: () => true, name: 'Found App1.app' },
        { isDirectory: () => true, name: 'Found App2.app' },
        { isDirectory: () => true, name: 'Found App3.app' },
        { isDirectory: () => true, name: 'Ignored App.app' },
      ] as any)
      setupExecMock(BREW_COMMANDS.INFO_CASK('found-app1'), mockExecuteCommandResult('found-app1: 1.0'))
      setupExecMock(BREW_COMMANDS.INFO_CASK('found-app2'), mockExecuteCommandResult('', 'Error: No Cask with this name exists.', 1))
      setupExecMock(BREW_COMMANDS.INFO_FORMULA('found-app2'), mockExecuteCommandResult('found-app2: 1.0'))
      setupExecMock(BREW_COMMANDS.INFO_CASK('found-app3'), mockExecuteCommandResult('', 'Error: No Cask with this name exists.', 1))
      setupExecMock(BREW_COMMANDS.INFO_FORMULA('found-app3'), mockExecuteCommandResult('', 'Error: No available formula with this name.', 1))
      setupExecMock(BREW_COMMANDS.LIST_CASKS, mockExecuteCommandResult('found-app1\
some-other-cask'))
      setupExecMock(BREW_COMMANDS.LIST_FORMULAS, mockExecuteCommandResult(''))
      const config: ScannerConfig = { ...baseConfig, ignoredApps: ['Ignored App'] }
      const results = await appScanner.discoverApps(config)
      expect(results.length).toBe(4)
      const app1Info = results.find(app => app.originalName === 'Found App1')
      expect(app1Info?.brewType).toBe('cask')
      expect(app1Info?.status).toBe('already-installed')
      expect(app1Info?.alreadyInstalled).toBe(true)
      const app2Info = results.find(app => app.originalName === 'Found App2')
      expect(app2Info?.brewType).toBe('formula')
      expect(app2Info?.status).toBe('available')
      expect(app2Info?.alreadyInstalled).toBe(false)
      const app3Info = results.find(app => app.originalName === 'Found App3')
      expect(app3Info?.brewType).toBe('unavailable')
      expect(app3Info?.status).toBe('unavailable')
      const ignoredAppInfo = results.find(app => app.originalName === 'Ignored App')
      expect(ignoredAppInfo?.status).toBe('ignored')
    })
  })
})
