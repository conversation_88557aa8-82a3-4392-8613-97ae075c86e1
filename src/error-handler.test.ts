import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

import { EXIT_CODES, MESSAGES } from './constants.js'
import { createProgress<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON>r<PERSON><PERSON><PERSON>, initialize<PERSON>rror<PERSON>and<PERSON>, ProgressTracker, setupGlobalErrorHandlers } from './error-handler.js'
import { ConvertAppsError, ErrorType } from './types.js'
import * as utilities from './utils.js'
// Mock process.exit
const mockProcessExit = vi.spyOn(process, 'exit').mockImplementation((code?: null | number | string | undefined): never => {
  // process.exit can take a string, but it usually results in exit code 1.
  // For testing, we'll just record what was passed or a generic error for strings.
  const exitCodeForError = typeof code === 'number' ? code : (code === undefined || code === null ? 'undefined' : 1)
  throw new Error(`process.exit: ${exitCodeForError}`)
})
// Mock console methods
const mockConsoleLog = vi.fn()
const mockConsoleError = vi.fn()
const mockConsoleWarn = vi.fn()
describe('errorHandler', () => {
  let errorHandler: ErrorHandler
  beforeEach(() => {
    vi.spyOn(console, 'log').mockImplementation(mockConsoleLog)
    vi.spyOn(console, 'error').mockImplementation(mockConsoleError)
    vi.spyOn(console, 'warn').mockImplementation(mockConsoleWarn)
    mockProcessExit.mockClear()
    mockConsoleLog.mockClear()
    mockConsoleError.mockClear()
    mockConsoleWarn.mockClear()
    // Reset globalErrorHandler for consistent tests
    initializeErrorHandler(false) // Or pass true if default verbose is needed
    errorHandler = getErrorHandler()
  })
  afterEach(() => {
    vi.restoreAllMocks()
  })
  it('should handle ConvertAppsError - COMMAND_FAILED', () => {
    const error = new ConvertAppsError('Command failed', ErrorType.COMMAND_FAILED)
    expect(() => errorHandler.handleError(error, 'test context')).toThrow('process.exit: 1')
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('Command execution failed (test context): Command failed'))
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Command Failure Help:'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.GENERAL_ERROR)
  })
  it('should handle ConvertAppsError - FILE_NOT_FOUND', () => {
    const error = new ConvertAppsError('File missing', ErrorType.FILE_NOT_FOUND)
    expect(() => errorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('File not found: File missing'))
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('File Not Found Help:'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.GENERAL_ERROR)
  })
  it('should handle ConvertAppsError - HOMEBREW_NOT_INSTALLED', () => {
    const error = new ConvertAppsError(MESSAGES.HOMEBREW_NOT_INSTALLED, ErrorType.HOMEBREW_NOT_INSTALLED)
    expect(() => errorHandler.handleError(error)).toThrow(`process.exit: ${EXIT_CODES.HOMEBREW_NOT_INSTALLED}`)
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining(MESSAGES.HOMEBREW_NOT_INSTALLED))
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Homebrew Installation Help:'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.HOMEBREW_NOT_INSTALLED)
  })
  it('should handle ConvertAppsError - INVALID_INPUT', () => {
    const error = new ConvertAppsError('Bad input', ErrorType.INVALID_INPUT)
    expect(() => errorHandler.handleError(error)).toThrow(`process.exit: ${EXIT_CODES.INVALID_INPUT}`)
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('Invalid input: Bad input'))
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Input Validation Help:'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.INVALID_INPUT)
  })
  it('should handle ConvertAppsError - NETWORK_ERROR', () => {
    const error = new ConvertAppsError('Connection timeout', ErrorType.NETWORK_ERROR)
    expect(() => errorHandler.handleError(error)).toThrow(`process.exit: ${EXIT_CODES.NETWORK_ERROR}`)
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('Network error occurred. Please check your internet connection.'))
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Network Help:'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.NETWORK_ERROR)
  })
  it('should handle ConvertAppsError - PERMISSION_DENIED', () => {
    const error = new ConvertAppsError('Access denied', ErrorType.PERMISSION_DENIED)
    expect(() => errorHandler.handleError(error)).toThrow(`process.exit: ${EXIT_CODES.PERMISSION_DENIED}`)
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining(MESSAGES.PERMISSION_DENIED))
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Permission Help:'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.PERMISSION_DENIED)
  })
  it('should handle ConvertAppsError - UNKNOWN_ERROR', () => {
    const originalError = new Error('original')
    const error = new ConvertAppsError('Something unknown', ErrorType.UNKNOWN_ERROR, originalError)
    const verboseErrorHandler = new ErrorHandler(true) // For verbose check
    expect(() => verboseErrorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('Unknown error: Something unknown'))
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Original error: original')) // debug log
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.GENERAL_ERROR)
  })
  it('should handle ConvertAppsError - default case', () => {
    // @ts-expect-error Testing default case with a non-existent error type
    const error = new ConvertAppsError('Default case', 'NON_EXISTENT_TYPE')
    expect(() => errorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('Application error: Default case'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.GENERAL_ERROR)
  })
  it('should handle generic Error', () => {
    const error = new Error('Generic error')
    expect(() => errorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleError).toHaveBeenCalledWith(expect.stringContaining('Unexpected error: Generic error'))
    expect(mockProcessExit).toHaveBeenCalledWith(EXIT_CODES.GENERAL_ERROR)
  })
  it('should show specific help for generic ENOENT error', () => {
    const error = new Error('Something ENOENT else')
    expect(() => errorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('File Not Found Help:'))
  })
  it('should show specific help for generic EACCES error', () => {
    const error = new Error('Denied EACCES access')
    expect(() => errorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Permission Help:'))
  })
  it('should show specific help for generic ENOTDIR error', () => {
    const error = new Error('Path ENOTDIR problem')
    expect(() => errorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('The specified path is not a directory.'))
  })
  it('should show specific help for generic spawn ENOENT error', () => {
    const error = new Error('spawn CMD ENOENT')
    expect(() => errorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Command not found. Make sure Homebrew is installed and in your PATH.'))
  })
  it('should log stack trace for generic error when verbose is true', () => {
    const error = new Error('Generic verbose error')
    error.stack = 'Error stack trace'
    const verboseErrorHandler = new ErrorHandler(true)
    expect(() => verboseErrorHandler.handleError(error)).toThrow('process.exit: 1')
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Stack trace: Error stack trace'))
  })
})
describe('progressTracker', () => {
  let tracker: ProgressTracker
  beforeEach(() => {
    vi.spyOn(console, 'log').mockImplementation(mockConsoleLog)
    mockConsoleLog.mockClear()
    tracker = new ProgressTracker(false) // Non-verbose by default
    vi.useFakeTimers()
  })
  afterEach(() => {
    vi.restoreAllMocks()
    vi.useRealTimers()
  })
  it('startOperation should log start message', () => {
    tracker.startOperation('Test Op')
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('ð Starting Test Op...'))
  })
  it('startOperation should log start message with total items', () => {
    tracker.startOperation('Test Op Items', 10)
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('ð Starting Test Op Items (10 items)...'))
  })
  it('completeOperation should log success message', () => {
    tracker.startOperation('Success Op')
    vi.advanceTimersByTime(1234)
    tracker.completeOperation('Success Op', true)
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('â Success Op completed in 1.2s'))
  })
  it('completeOperation should log warning message on failure', () => {
    tracker.startOperation('Fail Op')
    vi.advanceTimersByTime(567)
    tracker.completeOperation('Fail Op', false)
    expect(mockConsoleWarn).toHaveBeenCalledWith(expect.stringContaining('â ï¸  Fail Op completed with errors in 0.6s'))
  })
  it('updateProgress should log progress with progress bar', () => {
    tracker.startOperation('Progressing Op', 100)
    vi.advanceTimersByTime(1001) // Ensure enough time passed
    tracker.updateProgress('Doing stuff', 25, 100)
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Doing stuff [ââââââââââââââââââââ] 25/100 (25%)'))
  })
  it('updateProgress should include elapsed time for long operations', () => {
    tracker.startOperation('Long Op', 10)
    vi.advanceTimersByTime(6000)
    tracker.updateProgress('Still going', 5, 10)
    expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('[6s]'))
  })
  it('updateProgress should not log if insufficient time has passed', () => {
    tracker.startOperation('Quick Op', 10)
    tracker.updateProgress('tick', 1, 10)
    expect(mockConsoleLog).toHaveBeenCalledTimes(1) // Only startOperation
    vi.advanceTimersByTime(500)
    tracker.updateProgress('tock', 2, 10)
    expect(mockConsoleLog).toHaveBeenCalledTimes(1) // Still only startOperation
  })
  it('updateProgress should log when current equals total', () => {
    tracker.startOperation('Finishing Op', 10)
    vi.advanceTimersByTime(50) // Short time
    tracker.updateProgress('Almost done', 10, 10)
    expect(mockConsoleLog).toHaveBeenCalledTimes(2) // Start and this update
    expect(mockConsoleLog).toHaveBeenLastCalledWith(expect.stringContaining('Almost done [ââââââââââââââââââââ] 10/10 (100%)'))
  })
})
describe('global Error Handler Functions', () => {
  beforeEach(() => {
    vi.spyOn(console, 'log').mockImplementation(mockConsoleLog)
    vi.spyOn(console, 'error').mockImplementation(mockConsoleError)
    mockProcessExit.mockClear()
    mockConsoleLog.mockClear()
    mockConsoleError.mockClear()
  })
  afterEach(() => {
    vi.restoreAllMocks()
    // Clean up global listeners if they were attached
    process.removeAllListeners('uncaughtException')
    process.removeAllListeners('unhandledRejection')
  })
  it('initializeErrorHandler should create and return an ErrorHandler instance', () => {
    const handler = initializeErrorHandler(true)
    expect(handler).toBeInstanceOf(ErrorHandler)
    // @ts-expect-error checking private property
    expect(handler.verbose).toBe(true)
  })
  it('getErrorHandler should return the same instance', () => {
    const handler1 = initializeErrorHandler()
    const handler2 = getErrorHandler()
    expect(handler1).toBe(handler2)
  })
  it('createProgressCallback should return a function that calls tracker.updateProgress', () => {
    const mockTracker = { updateProgress: vi.fn() } as unknown as ProgressTracker
    const callback = createProgressCallback(mockTracker)
    callback('Test message', 1, 10) // eslint-disable-line @typescript-eslint/unbound-method
    expect(mockTracker.updateProgress).toHaveBeenCalledWith('Test message', 1, 10)
  })
  it('setupGlobalErrorHandlers should set up process listeners', () => {
    const onSpy = vi.spyOn(process, 'on')
    setupGlobalErrorHandlers(false)
    expect(onSpy).toHaveBeenCalledWith('uncaughtException', expect.any(Function))
    expect(onSpy).toHaveBeenCalledWith('unhandledRejection', expect.any(Function))
  })
  // Note: Testing the actual behavior of uncaughtException/unhandledRejection
  // is complex in Vitest and might require emitting events manually or more involved setup.
  // These tests primarily check that handlers are attached.
})
