import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'

import * as prompts from './prompts.js'
import type { AppInfo, CommandOptions } from './types.js'
import { createLogger } from './utils.js' // For logger in tests
// Mock @inquirer/checkbox
const mockCheckbox = vi.fn()
vi.mock('@inquirer/checkbox', () => ({
  default: mockCheckbox,
}))
// Mock @inquirer/password
const mockPassword = vi.fn()
vi.mock('@inquirer/password', () => ({
  default: mockPassword,
}))
// Mock console methods
const mockConsoleLog = vi.fn()
const mockConsoleWarn = vi.fn()
const baseAppInfo: AppInfo = {
  alreadyInstalled: false,
  appPath: '/Applications/Test App.app',
  brewName: 'test-app',
  brewType: 'cask',
  originalName: 'Test App',
  status: 'available',
}

describe('prompts', () => {
  beforeEach(() => {
    vi.spyOn(console, 'log').mockImplementation(mockConsoleLog)
    vi.spyOn(console, 'warn').mockImplementation(mockConsoleWarn)
    mockCheckbox.mockReset()
    mockPassword.mockReset()
    mockConsoleLog.mockClear()
    mockConsoleWarn.mockClear()
  })
  afterEach(() => {
    vi.restoreAllMocks()
  })
  describe('displayFinalSummary', () => {
    it('should display summary for successful installation', () => {
      const selected: AppInfo[] = [{ ...baseAppInfo, originalName: 'App1' }]
      const installed: AppInfo[] = [{ ...baseAppInfo, originalName: 'App1' }]
      prompts.displayFinalSummary(selected, installed, [], false)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Installation Complete'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Successfully installed (1):'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('App1'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Thank you for using convert-apps-to-homebrew!'))
    })
    it('should display summary for dry run', () => {
      const selected: AppInfo[] = [{ ...baseAppInfo, brewType: 'cask', originalName: 'App1' }, { ...baseAppInfo, brewType: 'formula', originalName: 'App2' }]
      prompts.displayFinalSummary(selected, [], [], true)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Dry Run Complete'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Would have processed 2 apps:'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('1 cask'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('1 formula'))
    })
    it('should display summary for failed installations', () => {
      const selected: AppInfo[] = [{ ...baseAppInfo, originalName: 'App1' }]
      const failed: AppInfo[] = [{ ...baseAppInfo, originalName: 'App1' }]
      prompts.displayFinalSummary(selected, [], failed, false)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Failed to install (1):'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('App1'))
    })
    it('should display "No apps were processed" if nothing installed or failed', () => {
      prompts.displayFinalSummary([], [], [], false)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('No apps were processed.'))
    })
  })
  describe('displayInstallationPlan', () => {
    const appCask: AppInfo = { ...baseAppInfo, brewName: 'cask-app', brewType: 'cask', originalName: 'CaskApp' }
    const appFormula: AppInfo = { ...baseAppInfo, brewName: 'formula-app', brewType: 'formula', originalName: 'FormulaApp' }
    it('should display plan for casks and formulas with sudo', () => {
      prompts.displayInstallationPlan([appCask, appFormula], 'sudoPass', false)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Installation Plan'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Casks to install (1):'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('CaskApp â cask-app'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Will delete original .app files (sudo access provided)'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Formulas to install (1):'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('FormulaApp â formula-app'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Original .app files will be kept'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Ready to proceed with installation.'))
    })
    it('should display plan for casks without sudo', () => {
      prompts.displayInstallationPlan([appCask], undefined, false)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Will skip deletion of original .app files (no sudo access)'))
    })
    it('should display dry run information', () => {
      prompts.displayInstallationPlan([appCask], 'sudoPass', true)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Installation Plan (DRY RUN)'))
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('This is a dry run - no actual changes will be made.'))
    })
    it('should not display if no apps selected', () => {
      prompts.displayInstallationPlan([], undefined, false)
      expect(mockConsoleLog).not.toHaveBeenCalledWith(expect.stringContaining('Installation Plan'))
    })
  })
  describe('promptAppSelection', () => {
    const app1: AppInfo = { ...baseAppInfo, brewName: 'app-one', originalName: 'App One', status: 'available' }
    const app2: AppInfo = { ...baseAppInfo, brewName: 'app-two', brewType: 'formula', originalName: 'App Two', status: 'available' }
    const appInstalled: AppInfo = { ...baseAppInfo, originalName: 'Installed App', status: 'already-installed' }
    const options: CommandOptions = { verbose: false }
    it('should return selected apps', async () => {
      mockCheckbox.mockResolvedValueOnce([app1])
      const selected = await prompts.promptAppSelection([app1, app2, appInstalled], options)
      expect(mockCheckbox).toHaveBeenCalled()
      expect(selected).toEqual([app1])
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Selected 1 app for installation.'))
    })
    it('should return empty array if no apps available', async () => {
      const selected = await prompts.promptAppSelection([appInstalled], options)
      expect(mockCheckbox).not.toHaveBeenCalled()
      expect(selected).toEqual([])
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('No apps available for selection.'))
    })
    it('should return empty array if user selects nothing', async () => {
      mockCheckbox.mockResolvedValueOnce([])
      const selected = await prompts.promptAppSelection([app1], options)
      expect(selected).toEqual([])
      expect(mockConsoleWarn).toHaveBeenCalledWith('No applications selected for installation.')
    })
    it('should return empty array if user cancels (ExitPromptError)', async () => {
      const exitError = new Error('User cancelled')
      exitError.name = 'ExitPromptError'
      mockCheckbox.mockRejectedValueOnce(exitError)
      const selected = await prompts.promptAppSelection([app1], options)
      expect(selected).toEqual([])
      expect(mockConsoleWarn).toHaveBeenCalledWith('Selection cancelled by user.')
    })
    it('should re-throw other errors', async () => {
      const otherError = new Error('Some other error')
      mockCheckbox.mockRejectedValueOnce(otherError)
      await expect(prompts.promptAppSelection([app1], options)).rejects.toThrow('Some other error')
    })
  })
  describe('promptConfirmation', () => {
    // Note: This prompt is auto-confirming in the current implementation for tests
    it('should auto-confirm for non-dry run', async () => {
      const confirmed = await prompts.promptConfirmation(false)
      expect(confirmed).toBe(true)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Proceed with installation? (y/N):'))
    })
    it('should auto-confirm for dry run', async () => {
      const confirmed = await prompts.promptConfirmation(true)
      expect(confirmed).toBe(true)
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Proceed with dry run? (y/N):'))
    })
  })
  describe('promptSudoPassword', () => {
    const caskApp: AppInfo = { ...baseAppInfo, brewType: 'cask' }
    const formulaApp: AppInfo = { ...baseAppInfo, brewType: 'formula' }
    it('should prompt for password if casks are selected', async () => {
      mockPassword.mockResolvedValueOnce('testpass')
      const password = await prompts.promptSudoPassword([caskApp, formulaApp])
      expect(mockPassword).toHaveBeenCalled()
      expect(password).toBe('testpass')
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Administrator Access Required'))
    })
    it('should not prompt if only formulas are selected', async () => {
      const password = await prompts.promptSudoPassword([formulaApp])
      expect(mockPassword).not.toHaveBeenCalled()
      expect(password).toBeUndefined()
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('No sudo password needed (no cask installations).'))
    })
    it('should return undefined if no password entered', async () => {
      mockPassword.mockResolvedValueOnce('') // Empty password
      const password = await prompts.promptSudoPassword([caskApp])
      expect(password).toBeUndefined()
      expect(mockConsoleWarn).toHaveBeenCalledWith('No password provided. Cask installations will be skipped.')
    })
    it('should return undefined if prompt cancelled (ExitPromptError)', async () => {
      const exitError = new Error('User cancelled')
      exitError.name = 'ExitPromptError'
      mockPassword.mockRejectedValueOnce(exitError)
      const password = await prompts.promptSudoPassword([caskApp])
      expect(password).toBeUndefined()
      expect(mockConsoleWarn).toHaveBeenCalledWith('Password prompt cancelled by user.')
    })
    it('should re-throw other errors from password prompt', async () => {
      const otherError = new Error('Password prompt failed')
      mockPassword.mockRejectedValueOnce(otherError)
      await expect(prompts.promptSudoPassword([caskApp])).rejects.toThrow('Password prompt failed')
    })
  })
})
