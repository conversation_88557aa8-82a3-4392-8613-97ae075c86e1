import { describe, expect, it } from 'vitest'

import * as constants from './constants.js'

describe('constants', () => {
  it('dEFAULT_APPLICATIONS_DIR should be correct', () => {
    expect(constants.DEFAULT_APPLICATIONS_DIR).toBe('/Applications')
  })
  describe('bREW_COMMANDS', () => {
    it('should have correct static commands', () => {
      expect(constants.BREW_COMMANDS.LIST_CASKS).toBe('brew ls -1 --cask')
      expect(constants.BREW_COMMANDS.LIST_FORMULAS).toBe('brew leaves')
      expect(constants.BREW_COMMANDS.VERSION).toBe('brew --version')
    })
    it('iNFO_CASK should generate correct command', () => {
      expect(constants.BREW_COMMANDS.INFO_CASK('my-cask')).toBe('brew info --cask "my-cask"')
    })
    it('iNFO_FORMULA should generate correct command', () => {
      expect(constants.BREW_COMMANDS.INFO_FORMULA('my-formula')).toBe('brew info "my-formula"')
    })
    it('iNSTALL_CASK should generate correct command for single cask', () => {
      expect(constants.BREW_COMMANDS.INSTALL_CASK(['my-cask'])).toBe('brew install --cask "my-cask"')
    })
    it('iNSTALL_CASK should generate correct command for multiple casks', () => {
      expect(constants.BREW_COMMANDS.INSTALL_CASK(['cask1', 'cask2'])).toBe('brew install --cask "cask1" "cask2"')
    })
    it('iNSTALL_FORMULA should generate correct command for single formula', () => {
      expect(constants.BREW_COMMANDS.INSTALL_FORMULA(['my-formula'])).toBe('brew install "my-formula"')
    })
    it('iNSTALL_FORMULA should generate correct command for multiple formulas', () => {
      expect(constants.BREW_COMMANDS.INSTALL_FORMULA(['formula1', 'formula2'])).toBe('brew install "formula1" "formula2"')
    })
  })
  describe('fILE_PATTERNS', () => {
    it('should have correct patterns', () => {
      expect(constants.FILE_PATTERNS.APP_EXTENSION).toBe('.app')
      expect(constants.FILE_PATTERNS.APP_PATTERN).toEqual(/\.app$/i)
    })
    it('aPP_PATTERN should match .app files', () => {
      expect(constants.FILE_PATTERNS.APP_PATTERN.test('TestApp.app')).toBe(true)
      expect(constants.FILE_PATTERNS.APP_PATTERN.test('Another.App')).toBe(true)
    })
    it('aPP_PATTERN should not match non-.app files', () => {
      expect(constants.FILE_PATTERNS.APP_PATTERN.test('TestApp.exe')).toBe(false)
      expect(constants.FILE_PATTERNS.APP_PATTERN.test('document.txt')).toBe(false)
    })
  })
  describe('eXIT_CODES', () => {
    it('should have correct exit codes', () => {
      expect(constants.EXIT_CODES.SUCCESS).toBe(0)
      expect(constants.EXIT_CODES.GENERAL_ERROR).toBe(1)
      expect(constants.EXIT_CODES.HOMEBREW_NOT_INSTALLED).toBe(2)
      expect(constants.EXIT_CODES.PERMISSION_DENIED).toBe(3)
      expect(constants.EXIT_CODES.INVALID_INPUT).toBe(4)
      expect(constants.EXIT_CODES.NETWORK_ERROR).toBe(5)
    })
  })
  describe('dEFAULT_CONFIG', () => {
    it('should have correct default config values', () => {
      expect(constants.DEFAULT_CONFIG.BREW_COMMAND_TIMEOUT).toBe(30_000)
      expect(constants.DEFAULT_CONFIG.DRY_RUN).toBe(false)
      expect(constants.DEFAULT_CONFIG.MAX_CONCURRENT_OPERATIONS).toBe(5)
      expect(constants.DEFAULT_CONFIG.VERBOSE).toBe(false)
    })
  })
  describe('cOLORS', () => {
    it('should have correct ANSI color codes', () => {
      expect(constants.COLORS.RED).toBe('\u001B[31m')
      expect(constants.COLORS.GREEN).toBe('\u001B[32m')
      expect(constants.COLORS.YELLOW).toBe('\u001B[33m')
      expect(constants.COLORS.BLUE).toBe('\u001B[34m')
      expect(constants.COLORS.MAGENTA).toBe('\u001B[35m')
      expect(constants.COLORS.CYAN).toBe('\u001B[36m')
      expect(constants.COLORS.WHITE).toBe('\u001B[37m')
      expect(constants.COLORS.RESET).toBe('\u001B[0m')
      expect(constants.COLORS.BRIGHT).toBe('\u001B[1m')
      expect(constants.COLORS.DIM).toBe('\u001B[2m')
    })
  })
  describe('mESSAGES', () => {
    it('should have correct message strings', () => {
      expect(typeof constants.MESSAGES.CHECKING_HOMEBREW).toBe('string')
      expect(typeof constants.MESSAGES.DELETING_APPS).toBe('string')
      // Add more checks if specific message content needs to be verified
      expect(constants.MESSAGES.HOMEBREW_NOT_INSTALLED).toBe('Homebrew is not installed. Please install it before continuing.')
    })
  })
  describe('rEGEX_PATTERNS', () => {
    it('aPP_NAME should validate app names correctly', () => {
      expect(constants.REGEX_PATTERNS.APP_NAME.test('ValidApp')).toBe(true)
      expect(constants.REGEX_PATTERNS.APP_NAME.test('App With Spaces')).toBe(true)
      expect(constants.REGEX_PATTERNS.APP_NAME.test('App-With-Hyphens')).toBe(true)
      expect(constants.REGEX_PATTERNS.APP_NAME.test('App_With_Underscores')).toBe(true)
      expect(constants.REGEX_PATTERNS.APP_NAME.test('App.With.Dots')).toBe(true)
      expect(constants.REGEX_PATTERNS.APP_NAME.test('Invalid/App')).toBe(false)
      expect(constants.REGEX_PATTERNS.APP_NAME.test('Invalid\0App')).toBe(false)
      expect(constants.REGEX_PATTERNS.APP_NAME.test('')).toBe(false) // Empty string
    })
    it('bREW_PACKAGE_NAME should validate package names correctly', () => {
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('valid-package')).toBe(true)
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('package1')).toBe(true)
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('package.name')).toBe(true)
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('p')).toBe(true)
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('PKG')).toBe(true) // Case-insensitive flag
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('-invalid-start')).toBe(false)
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('invalid_char!')).toBe(false)
      expect(constants.REGEX_PATTERNS.BREW_PACKAGE_NAME.test('')).toBe(false)
    })
    it('vERSION should validate version strings correctly', () => {
      expect(constants.REGEX_PATTERNS.VERSION.test('1.0.0')).toBe(true)
      expect(constants.REGEX_PATTERNS.VERSION.test('12.34.56')).toBe(true)
      expect(constants.REGEX_PATTERNS.VERSION.test('0.0.0')).toBe(true)
      expect(constants.REGEX_PATTERNS.VERSION.test('1.0.0-beta')).toBe(true) // Matches starting part
      expect(constants.REGEX_PATTERNS.VERSION.test('1.0')).toBe(false)
      expect(constants.REGEX_PATTERNS.VERSION.test('1')).toBe(false)
      expect(constants.REGEX_PATTERNS.VERSION.test('a.b.c')).toBe(false)
      expect(constants.REGEX_PATTERNS.VERSION.test('')).toBe(false)
    })
  })
})
