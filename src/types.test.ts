import { describe, expect, it } from 'vitest'

import type { ErrorTypeValue } from './types.js'
import { ConvertAppsError, ErrorType } from './types.js'

describe('type Definitions', () => {
  describe('errorType', () => {
    it('should contain defined error types', () => {
      expect(ErrorType.COMMAND_FAILED).toBe('COMMAND_FAILED')
      expect(ErrorType.FILE_NOT_FOUND).toBe('FILE_NOT_FOUND')
      expect(ErrorType.HOMEBREW_NOT_INSTALLED).toBe('HOMEBREW_NOT_INSTALLED')
      expect(ErrorType.INVALID_INPUT).toBe('INVALID_INPUT')
      expect(ErrorType.NETWORK_ERROR).toBe('NETWORK_ERROR')
      expect(ErrorType.PERMISSION_DENIED).toBe('PERMISSION_DENIED')
      expect(ErrorType.UNKNOWN_ERROR).toBe('UNKNOWN_ERROR')
    })
    it('should be a frozen object', () => {
      expect(Object.isFrozen(ErrorType)).toBe(true)
    })
  })
  describe('convertAppsError', () => {
    it('should create an instance with message and type', () => {
      const message = 'Test error message'
      const type: ErrorTypeValue = ErrorType.COMMAND_FAILED
      const error = new ConvertAppsError(message, type)
      expect(error).toBeInstanceOf(Error)
      expect(error).toBeInstanceOf(ConvertAppsError)
      expect(error.message).toBe(message)
      expect(error.name).toBe('ConvertAppsError')
      expect(error.type).toBe(type)
      expect(error.originalError).toBeUndefined()
    })
    it('should create an instance with message, type, and originalError', () => {
      const message = 'Wrapper error message'
      const type: ErrorTypeValue = ErrorType.UNKNOWN_ERROR
      const originalError = new Error('Original underlying error')
      const error = new ConvertAppsError(message, type, originalError)
      expect(error.message).toBe(message)
      expect(error.type).toBe(type)
      expect(error.originalError).toBe(originalError)
      expect(error.originalError?.message).toBe('Original underlying error')
    })
    it('stack trace should be available', () => {
      const error = new ConvertAppsError('Test stack', ErrorType.INVALID_INPUT)
      expect(error.stack).toBeDefined()
      expect(typeof error.stack).toBe('string')
    })
  })
  // Placeholder for other types if they had runtime behavior
  it('should confirm other types are primarily for compile-time checks', () => {
    expect(true).toBe(true) // Placeholder assertion
  })
})
