import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import * as utils from './utils.js'
import { COLORS } from './constants.js'
describe('Utility Functions', () => {
  describe('capitalize', () => {
    it('should capitalize the first letter of a string', () => {
      expect(utils.capitalize('hello')).toBe('Hello')
      expect(utils.capitalize('world')).toBe('World')
      expect(utils.capitalize('AlreadyCaps')).toBe('AlreadyCaps')
      expect(utils.capitalize('123test')).toBe('123test')
      expect(utils.capitalize('')).toBe('')
    })
  })
  describe('colorize', () => {
    it('should wrap text with ANSI color codes', () => {
      expect(utils.colorize('hello', 'RED')).toBe(`${COLORS.RED}hello${COLORS.RESET}`)
      expect(utils.colorize('world', 'GREEN')).toBe(`${COLORS.GREEN}world${COLORS.RESET}`)
    })
  })
  describe('createLogger', () => {
    const mockConsoleLog = vi.fn()
    const mockConsoleError = vi.fn()
    const mockConsoleWarn = vi.fn()
    beforeEach(() => {
      vi.spyOn(console, 'log').mockImplementation(mockConsoleLog)
      vi.spyOn(console, 'error').mockImplementation(mockConsoleError)
      vi.spyOn(console, 'warn').mockImplementation(mockConsoleWarn)
    })
    afterEach(() => {
      vi.restoreAllMocks()
      mockConsoleLog.mockClear()
      mockConsoleError.mockClear()
      mockConsoleWarn.mockClear()
    })
    it('logger.info should call console.log with blue color', () => {
      const logger = utils.createLogger()
      logger.info('Test info')
      expect(mockConsoleLog).toHaveBeenCalledWith(utils.colorize('â¹ Test info', 'BLUE'))
    })
    it('logger.error should call console.error with red color', () => {
      const logger = utils.createLogger()
      logger.error('Test error')
      expect(mockConsoleError).toHaveBeenCalledWith(utils.colorize('â Test error', 'RED'))
    })
    it('logger.warn should call console.warn with yellow color', () => {
      const logger = utils.createLogger()
      logger.warn('Test warn')
      expect(mockConsoleWarn).toHaveBeenCalledWith(utils.colorize('â  Test warn', 'YELLOW'))
    })
    it('logger.debug should not log when verbose is false', () => {
      const logger = utils.createLogger(false)
      logger.debug('Test debug')
      expect(mockConsoleLog).not.toHaveBeenCalled()
    })
    it('logger.debug should log with magenta color when verbose is true', () => {
      const logger = utils.createLogger(true)
      logger.debug('Test debug')
      expect(mockConsoleLog).toHaveBeenCalledWith(utils.colorize('ð Test debug', 'MAGENTA'))
    })
    it('logger.verbose should not log when verbose is false', () => {
      const logger = utils.createLogger(false)
      logger.verbose('Test verbose')
      expect(mockConsoleLog).not.toHaveBeenCalled()
    })
    it('logger.verbose should log with dim color when verbose is true', () => {
      const logger = utils.createLogger(true)
      logger.verbose('Test verbose')
      expect(mockConsoleLog).toHaveBeenCalledWith(utils.colorize('ð Test verbose', 'DIM'))
    })
  })
  describe('createProgressBar', () => {
    it('should create a progress bar string', () => {
      expect(utils.createProgressBar(10, 100, 20)).toBe('[ââââââââââââââââââââ] 10% (10/100)')
      expect(utils.createProgressBar(50, 100, 20)).toBe('[ââââââââââââââââââââ] 50% (50/100)')
      expect(utils.createProgressBar(100, 100, 20)).toBe('[ââââââââââââââââââââ] 100% (100/100)')
      expect(utils.createProgressBar(0, 100, 10)).toBe('[ââââââââââ] 0% (0/100)')
      expect(utils.createProgressBar(150, 100, 20)).toBe('[ââââââââââââââââââââ] 100% (150/100)')
    })
  })
  describe('escapeShellArgument', () => {
    it('should wrap argument in double quotes', () => {
      expect(utils.escapeShellArgument('simple')).toBe('"simple"')
    })
    it('should escape inner double quotes', () => {
      expect(utils.escapeShellArgument('arg "with" quotes')).toBe(String.raw`"arg \"with\" quotes"`)
    })
    it('should handle empty string', () => {
      expect(utils.escapeShellArgument('')).toBe('""')
    })
    it('should handle arguments with spaces', () => {
      expect(utils.escapeShellArgument('argument with spaces')).toBe('"argument with spaces"')
    })
  })
  describe('extractAppName', () => {
    it('should extract app name from full path', () => {
      expect(utils.extractAppName('/Applications/Google Chrome.app')).toBe('Google Chrome')
    })
    it('should extract app name if path has no extension but ends with .app', () => {
      expect(utils.extractAppName('/Applications/Another App.app')).toBe('Another App')
    })
    it('should be case-insensitive for .app extension', () => {
      expect(utils.extractAppName('/Applications/MyApp.APP')).toBe('MyApp')
    })
    it('should return empty string if path is invalid or does not contain app name', () => {
      expect(utils.extractAppName('')).toBe('')
      expect(utils.extractAppName('/Applications/')).toBe('')
      expect(utils.extractAppName('NoSlashes.app')).toBe('NoSlashes')
    })
     it('should handle app names with periods', () => {
      expect(utils.extractAppName('/Applications/Visual Studio Code.app')).toBe('Visual Studio Code')
    })
  })
  describe('formatDuration', () => {
    it('should format milliseconds to ms', () => {
      expect(utils.formatDuration(500)).toBe('500ms')
    })
    it('should format milliseconds to seconds', () => {
      expect(utils.formatDuration(2500)).toBe('2s')
    })
    it('should format milliseconds to minutes and seconds', () => {
      expect(utils.formatDuration(65500)).toBe('1m 5s')
      expect(utils.formatDuration(120000)).toBe('2m 0s')
    })
    it('should handle 0 ms', () => {
      expect(utils.formatDuration(0)).toBe('0ms')
    })
  })
  describe('formatList', () => {
    it('should format a list of strings with default indent', () => {
      const items = ['item1', 'item2', 'item3']
      const expected = '  * item1\
  * item2\
  * item3'
      expect(utils.formatList(items)).toBe(expected)
    })
    it('should format a list of strings with custom indent', () => {
      const items = ['apple', 'banana']
      const expected = '----* apple\
----* banana'
      expect(utils.formatList(items, '----')).toBe(expected)
    })
    it('should return empty string for empty list', () => {
      expect(utils.formatList([])).toBe('')
    })
  })
  describe('groupBy', () => {
    it('should group items by a key function', () => {
      const items = [
        { type: 'A', value: 1 },
        { type: 'B', value: 2 },
        { type: 'A', value: 3 },
        { type: 'C', value: 4 },
        { type: 'B', value: 5 },
      ]
      const grouped = utils.groupBy(items, item => item.type)
      expect(grouped['A']).toEqual([{ type: 'A', value: 1 }, { type: 'A', value: 3 }])
      expect(grouped['B']).toEqual([{ type: 'B', value: 2 }, { type: 'B', value: 5 }])
      expect(grouped['C']).toEqual([{ type: 'C', value: 4 }])
    })
    it('should return empty object for empty array', () => {
      expect(utils.groupBy([], (item: any) => item.type)).toEqual({})
    })
  })
  describe('isEmpty', () => {
    it('should return true for null or undefined', () => {
      expect(utils.isEmpty(null)).toBe(true)
      expect(utils.isEmpty(undefined)).toBe(true)
    })
    it('should return true for empty string or string with only whitespace', () => {
      expect(utils.isEmpty('')).toBe(true)
      expect(utils.isEmpty('   ')).toBe(true)
      expect(utils.isEmpty('\\t\
 ')).toBe(true)
    })
    it('should return false for non-empty string', () => {
      expect(utils.isEmpty('hello')).toBe(false)
      expect(utils.isEmpty('  a  ')).toBe(false)
    })
  })
  describe('isValidAppName', () => {
    it('should validate app names correctly', () => {
      expect(utils.isValidAppName('ValidApp')).toBe(true)
      expect(utils.isValidAppName('App With Spaces')).toBe(true)
      expect(utils.isValidAppName('App-With-Hyphens')).toBe(true)
      expect(utils.isValidAppName('Invalid/App')).toBe(false)
      expect(utils.isValidAppName('')).toBe(false)
      expect(utils.isValidAppName('  ')).toBe(false)
    })
  })
  describe('isValidBrewPackageName', () => {
    it('should validate brew package names correctly', () => {
      expect(utils.isValidBrewPackageName('valid-package')).toBe(true)
      expect(utils.isValidBrewPackageName('package1')).toBe(true)
      expect(utils.isValidBrewPackageName('package.name')).toBe(true)
      expect(utils.isValidBrewPackageName('-invalid-start')).toBe(false)
      expect(utils.isValidBrewPackageName('invalid_char!')).toBe(false)
      expect(utils.isValidBrewPackageName('google-chrome')).toBe(true)
      expect(utils.isValidBrewPackageName('Discord')).toBe(true)
    })
  })
  describe('normalizeAppName', () => {
    it('should normalize app names correctly', () => {
      expect(utils.normalizeAppName('Google Chrome')).toBe('google-chrome')
      expect(utils.normalizeAppName('Visual Studio Code')).toBe('visual-studio-code')
      expect(utils.normalizeAppName('My Awesome App 1.0')).toBe('my-awesome-app-1.0')
      expect(utils.normalizeAppName('  Leading And Trailing Spaces  ')).toBe('leading-and-trailing-spaces')
      expect(utils.normalizeAppName('App---With---Multiple---Hyphens')).toBe('app-with-multiple-hyphens')
      expect(utils.normalizeAppName('!@#SpecialChars#$%^')).toBe('specialchars')
      expect(utils.normalizeAppName('-App-')).toBe('app')
      expect(utils.normalizeAppName('---')).toBe('')
      expect(utils.normalizeAppName('')).toBe('')
    })
  })
  describe('parseCommandOutput', () => {
    it('should split output by lines, trim, and filter empty lines', () => {
      const output = 'line1\
  line2  \
\
line3\
'
      expect(utils.parseCommandOutput(output)).toEqual(['line1', 'line2', 'line3'])
    })
    it('should return empty array for empty or whitespace-only output', () => {
      expect(utils.parseCommandOutput('')).toEqual([])
      expect(utils.parseCommandOutput('  \
\\t\
  ')).toEqual([])
    })
  })
  describe('pluralize', () => {
    it('should return singular form for count 1', () => {
      expect(utils.pluralize('apple', 1)).toBe('apple')
    })
    it('should return plural form for count other than 1', () => {
      expect(utils.pluralize('apple', 0)).toBe('apples')
      expect(utils.pluralize('apple', 2)).toBe('apples')
      expect(utils.pluralize('apple', 10)).toBe('apples')
    })
    it('should use custom suffix if provided', () => {
      expect(utils.pluralize('box', 2, 'es')).toBe('boxes')
      expect(utils.pluralize('wolf', 0, 'ves')).toBe('wolfves')
    })
  })
  describe('sleep', () => {
    it('should resolve after specified milliseconds', async () => {
      vi.useFakeTimers()
      const promise = utils.sleep(100)
      expect(promise).toBeInstanceOf(Promise)
      vi.advanceTimersByTime(100)
      await expect(promise).resolves.toBeUndefined()
      vi.useRealTimers()
    })
  })
  describe('truncate', () => {
    it('should not truncate if string is shorter than or equal to maxLength', () => {
      expect(utils.truncate('hello', 10)).toBe('hello')
      expect(utils.truncate('world', 5)).toBe('world')
    })
    it('should truncate and add ellipsis if string is longer than maxLength', () => {
      expect(utils.truncate('very long string', 10)).toBe('very lo...')
      expect(utils.truncate('another example here', 15)).toBe('another exam...')
    })
    it('should handle maxLength less than 3 by returning ellipsis or part of string', () => {
      expect(utils.truncate('abcde', 3)).toBe('...')
      expect(utils.truncate('abcde', 2)).toBe('..')
      expect(utils.truncate('abcde', 1)).toBe('.')
      expect(utils.truncate('abcde', 0)).toBe('')
    })
  })
  describe('uniqueBy', () => {
    it('should remove duplicates based on a key function', () => {
      const items = [
        { id: 1, name: 'A' },
        { id: 2, name: 'B' },
        { id: 1, name: 'C' },
        { id: 3, name: 'D' },
        { id: 2, name: 'E' },
      ]
      const uniqueItems = utils.uniqueBy(items, item => item.id)
      expect(uniqueItems).toEqual([
        { id: 1, name: 'A' },
        { id: 2, name: 'B' },
        { id: 3, name: 'D' },
      ])
    })
    it('should return empty array for empty input', () => {
      expect(utils.uniqueBy([], (item: any) => item.id)).toEqual([])
    })
    it('should work with primitive types if keyFunction returns the item itself', () => {
      const numbers: number[] = [1, 2, 2, 3, 1, 4]
      expect(utils.uniqueBy(numbers, num => num)).toEqual([1, 2, 3, 4])
    })
  })
})
